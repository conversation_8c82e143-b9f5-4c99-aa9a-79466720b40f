{"__meta": {"id": "01JYRJ00JSZH4BSWT8393N562T", "datetime": "2025-06-27 11:22:34", "utime": **********.458735, "method": "GET", "uri": "/api/2024-12/invite-permissions", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751023353.78871, "end": **********.458763, "duration": 0.6700527667999268, "duration_str": "670ms", "measures": [{"label": "Booting", "start": 1751023353.78871, "relative_start": 0, "end": **********.362788, "relative_end": **********.362788, "duration": 0.***************, "duration_str": "574ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.362801, "relative_start": 0.****************, "end": **********.458766, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "95.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.378307, "relative_start": 0.****************, "end": **********.383765, "relative_end": **********.383765, "duration": 0.005457878112792969, "duration_str": "5.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.451883, "relative_start": 0.****************, "end": **********.452081, "relative_end": **********.452081, "duration": 0.00019788742065429688, "duration_str": "198μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.454799, "relative_start": 0.****************, "end": **********.454939, "relative_end": **********.454939, "duration": 0.0001399517059326172, "duration_str": "140μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/invite-permissions", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Invite/TeamInviteController.php:152-159</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01433, "accumulated_duration_str": "14.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.410307, "duration": 0.01374, "duration_str": "13.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 95.883}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Invite/TeamInviteController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\Invite\\TeamInviteController.php", "line": 154}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4392848, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TeamInviteController.php:154", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Invite/TeamInviteController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\Invite\\TeamInviteController.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=154", "ajax": false, "filename": "TeamInviteController.php", "line": "154"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.883, "width_percent": 4.117}]}, "models": {"data": {"App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/invite-permissions\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/invite-permissions", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions", "uri": "GET api/2024-12/invite-permissions", "controller": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Invite/TeamInviteController.php:152-159</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f413080-f40c-407a-a4fe-9f9e032adfe9\" target=\"_blank\">View in Telescope</a>", "duration": "674ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-539886105 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-539886105\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1908001355 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908001355\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1502299119 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhnR3dTUk1obWVrSjJDZmNnWW80VHc9PSIsInZhbHVlIjoiaytKWVJYZWVxRnpGSlNxVUNBTUxPa0NvbG5YVGJOcnJkYjRqZlg2enhyOHpFbXA5TjdxVXErZm8yUncvSWRVWGJ6TzZUYTJwcTVBOG9IMGFETm9pR3dscjc5MkQ2UTQ2b3I2NmU4Z3BvaWd4bW8vcVNiR1RrbjlaWEh4NGU0STgiLCJtYWMiOiIwNWY1Yjg1NGRiZGJmNWUzYzE0NzU5MWFiMTU5MGNiMTM1ZTcwMGE2MjliNTk5ZjFjN2E2MTkwY2QzZDE5OWQ0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkdqNHRtUkFQM3NtYldYMnJ5MTN0MVE9PSIsInZhbHVlIjoiaEFucnExODNnTTlPcHhTbEljRTBEVTg0cDNCVjJ5SmlvYU9OTW8wV1dOeUxNUlVGdHpVVlNaZUN2UVA3bGpySmxMOTk0VGZkdDBDWjRjbEdQRTRYRUQ5UkhJMnlHYWt4RTdTYnlaZnhwb3J4YlZ4QjFsRUtlRzdlUjY1Q1lDT0UzcVJKWG52Y1dCbHJ1RFg4ZHRoa2lnPT0iLCJtYWMiOiIyN2FkMzgwNzMxNjBkOGQyMmU1MThjNTRhMzRlMmMxNzFiNmFjMTkzMmNjYjc5YjUxMjM3Yjc0NzMxOWRmNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhnR3dTUk1obWVrSjJDZmNnWW80VHc9PSIsInZhbHVlIjoiaytKWVJYZWVxRnpGSlNxVUNBTUxPa0NvbG5YVGJOcnJkYjRqZlg2enhyOHpFbXA5TjdxVXErZm8yUncvSWRVWGJ6TzZUYTJwcTVBOG9IMGFETm9pR3dscjc5MkQ2UTQ2b3I2NmU4Z3BvaWd4bW8vcVNiR1RrbjlaWEh4NGU0STgiLCJtYWMiOiIwNWY1Yjg1NGRiZGJmNWUzYzE0NzU5MWFiMTU5MGNiMTM1ZTcwMGE2MjliNTk5ZjFjN2E2MTkwY2QzZDE5OWQ0IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImpMOXZSdzZQL2x2Ynl0SExpSis3cHc9PSIsInZhbHVlIjoiQndNN2dCdEJnL1lOZnBkejBCMFJqSC9XRU5RNVVuZmVMS0JVZVdueEE2a2xFcTA4akRZRjMzNVRqOHdVZy8rTmJUWTI0SUpzNTdiRUY5eCt5a2pGck83SzZOK1Z6dEZVUVhYU2VnWDZhRUlyNTU3YWxNckppK2V4bUdkNkpiaHoiLCJtYWMiOiIxNTZiMGZmYzc4ZDNlZWUyZDY5MGQ3NjA3YWIyNmRjZGQxMzNkNTI5ZTU1NzhlZDM3MTkzNzk4OWM4MGMwYjMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502299119\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1838753787 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|HXrrYhx2s3t7WfXhSc1UK53hBR79an3GtHE40ciAjroQFhCSWxa5jO23zU6N|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7wipx8yTU6Zxt4273OHC75xL5Y173l3is2OEtUWH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838753787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-313825489 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 11:22:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZFSW1HR1FtTlhFbUFKdEpuTkE3T3c9PSIsInZhbHVlIjoiSjNnUU1PS1lRcWlUTU5UZkpVRXBnLzBabnE4NklrK25Na3pEdmhJQXVHMXJjQnAyekR1eldMU0pGVGVWZ3dSelN2QlNTbXBBUlA4SHB6WGxUeDliRjJNUFV1VDZpMDhYWW45ZTBlaTU3SjZ0Qk4reGc2K3ZXS3BVZ0JIeENaZjYiLCJtYWMiOiI0NTQzN2UzZTViZmM3YTY1ZTRkM2U4NDFkNTE4ZWVkY2I3MDI0MjY2MTQ0YmQyODg2NDVhYmQwNjFiMWUzNWJlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IitLZG9mVUdZcGdXZEIyQng2T3IwVGc9PSIsInZhbHVlIjoicGpxcjRRdEQvK3FJZDllQ1J2UmU1Qk9pTGJlV3Vlb0hnRGNEcmJUNDZNaERqdEFoUmQ2QTZlUUlQc0hmZkd0RmZtQ2JnYnd4MkFDR1VuYU0vbXdDajBOOVMzQ0orTkdZWTQwcHdUTTNYcThQcElacGloYmJ5dzlGSDZsTDFyZnAiLCJtYWMiOiI3N2FhNDNiOWExNjUyMTRiNmQzZGFjMDM5ZmE5ZWVmNzE3ZjJhNDlhZTgxYmMyODJhMzVlYTMxMjg5NTE5NmU5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 13:22:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZFSW1HR1FtTlhFbUFKdEpuTkE3T3c9PSIsInZhbHVlIjoiSjNnUU1PS1lRcWlUTU5UZkpVRXBnLzBabnE4NklrK25Na3pEdmhJQXVHMXJjQnAyekR1eldMU0pGVGVWZ3dSelN2QlNTbXBBUlA4SHB6WGxUeDliRjJNUFV1VDZpMDhYWW45ZTBlaTU3SjZ0Qk4reGc2K3ZXS3BVZ0JIeENaZjYiLCJtYWMiOiI0NTQzN2UzZTViZmM3YTY1ZTRkM2U4NDFkNTE4ZWVkY2I3MDI0MjY2MTQ0YmQyODg2NDVhYmQwNjFiMWUzNWJlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 13:22:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IitLZG9mVUdZcGdXZEIyQng2T3IwVGc9PSIsInZhbHVlIjoicGpxcjRRdEQvK3FJZDllQ1J2UmU1Qk9pTGJlV3Vlb0hnRGNEcmJUNDZNaERqdEFoUmQ2QTZlUUlQc0hmZkd0RmZtQ2JnYnd4MkFDR1VuYU0vbXdDajBOOVMzQ0orTkdZWTQwcHdUTTNYcThQcElacGloYmJ5dzlGSDZsTDFyZnAiLCJtYWMiOiI3N2FhNDNiOWExNjUyMTRiNmQzZGFjMDM5ZmE5ZWVmNzE3ZjJhNDlhZTgxYmMyODJhMzVlYTMxMjg5NTE5NmU5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 13:22:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313825489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-429524993 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://localhost:8000/api/2024-12/invite-permissions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429524993\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/invite-permissions", "controller_action": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions"}, "badge": null}}