{"__meta": {"id": "01JYRHZYVD5X9W9TND8T48TB2M", "datetime": "2025-06-27 11:22:32", "utime": **********.686092, "method": "POST", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[11:22:24] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.743144, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.323449, "end": **********.686116, "duration": 9.362667083740234, "duration_str": "9.36s", "measures": [{"label": "Booting", "start": **********.323449, "relative_start": 0, "end": **********.905465, "relative_end": **********.905465, "duration": 0.****************, "duration_str": "582ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.905481, "relative_start": 0.****************, "end": **********.686119, "relative_end": 3.0994415283203125e-06, "duration": 8.***************, "duration_str": "8.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.932035, "relative_start": 0.****************, "end": **********.939122, "relative_end": **********.939122, "duration": 0.007086992263793945, "duration_str": "7.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.677594, "relative_start": 9.***************, "end": **********.677975, "relative_end": **********.677975, "duration": 0.0003809928894042969, "duration_str": "381μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.680592, "relative_start": 9.**************, "end": **********.680717, "relative_end": **********.680717, "duration": 0.00012493133544921875, "duration_str": "125μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.store", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>"}, "queries": {"count": 58, "nb_statements": 56, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09412999999999996, "accumulated_duration_str": "94.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9684708, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 4.728}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and (`id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.35672, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.728, "width_percent": 0.606}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 875}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 652}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.425035, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "UniqueManyToMany.php:55", "source": {"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FRules%2FUniqueManyToMany.php&line=55", "ajax": false, "filename": "UniqueManyToMany.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.333, "width_percent": 0.786}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.434507, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:230", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=230", "ajax": false, "filename": "Organization.php", "line": "230"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.119, "width_percent": 0}, {"sql": "insert into `organizations` (`shop_id`, `name`, `region`, `units`, `currency`, `updated_at`, `created_at`) values (null, 'Tanzayb', null, null, null, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [null, "<PERSON><PERSON><PERSON><PERSON>", null, null, null, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.435952, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "Organization.php:245", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=245", "ajax": false, "filename": "Organization.php", "line": "245"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.119, "width_percent": 3.028}, {"sql": "insert into `channels` (`organization_id`, `name`, `type`, `updated_at`, `created_at`) values (1, 'Tanzayb Store', 'shopify', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store", "shopify", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4473598, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Organization.php:328", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=328", "ajax": false, "filename": "Organization.php", "line": "328"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.147, "width_percent": 1.562}, {"sql": "select count(*) as aggregate from `locations` where `organization_id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 32, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.453989, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Location.php:36", "source": {"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=36", "ajax": false, "filename": "Location.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.709, "width_percent": 1.424}, {"sql": "insert into `locations` (`organization_id`, `name`, `default_location`, `updated_at`, `created_at`) values (1, 'Tanzayb Store Warehouse', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store Warehouse", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.458066, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Organization.php:334", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=334", "ajax": false, "filename": "Organization.php", "line": "334"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 12.132, "width_percent": 1.296}, {"sql": "insert into `channel_location` (`channel_id`, `location_id`, `updated_at`, `created_at`) values (1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.462733, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "Organization.php:339", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=339", "ajax": false, "filename": "Organization.php", "line": "339"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.428, "width_percent": 1.902}, {"sql": "insert into `versions` (`name`, `organization_id`, `is_default`, `currency`, `separator`, `updated_at`, `created_at`) values ('EN-US', 1, 1, 'USD', '.', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["EN-US", 1, 1, "USD", ".", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.470104, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "Organization.php:349", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=349", "ajax": false, "filename": "Organization.php", "line": "349"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.33, "width_percent": 2.358}, {"sql": "insert into `channel_version` (`channel_id`, `version_id`, `updated_at`, `created_at`) values (1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4767022, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Organization.php:355", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=355", "ajax": false, "filename": "Organization.php", "line": "355"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.688, "width_percent": 1.976}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `updated_at`, `created_at`) values ('Tanzayb', 1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.487082, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Folder.php:228", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=228", "ajax": false, "filename": "Folder.php", "line": "228"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.664, "width_percent": 1.753}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IktXRzZCLzNnNUdVWm1LanB5Rk5LUFE9PSIsInZhbHVlIjoiS21wMkh2NU5BdkkxRUtYbHBBK1dwQT09IiwibWFjIjoiYzYwNjA2MDUzMzI0MDUxMjU5YzhhYTI4Yzg5ZDVlYzIwYWQxMjI1MWQ0NWIzM2Y4NzIyNGQ5NDc3ODNhYmM5ZiIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-06-27 11:22:24' where `id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IktXRzZCLzNnNUdVWm1LanB5Rk5LUFE9PSIsInZhbHVlIjoiS21wMkh2NU5BdkkxRUtYbHBBK1dwQT09IiwibWFjIjoiYzYwNjA2MDUzMzI0MDUxMjU5YzhhYTI4Yzg5ZDVlYzIwYWQxMjI1MWQ0NWIzM2Y4NzIyNGQ5NDc3ODNhYmM5ZiIsInRhZyI6IiJ9", "2025-06-27 11:22:24", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 230}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.492088, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.417, "width_percent": 0.584}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Instagram', 1, 1, 'social_media', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Instagram", 1, 1, "social_media", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.495877, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.001, "width_percent": 0.521}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IjRMZzFiQnFmbk4yczl4WWpTRzczc0E9PSIsInZhbHVlIjoiMnlXSTZndjVab3JORVNoQm9GL2kxUT09IiwibWFjIjoiZGI3ZjcwYmRjMWUxYWRiMjczYTE0MmUyZmRiNzM1OWVlZjI0NTNiOTBjZmMxYTVkODEyMmMwZjgyYjE1NzM1MyIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-06-27 11:22:24' where `id` = 2 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IjRMZzFiQnFmbk4yczl4WWpTRzczc0E9PSIsInZhbHVlIjoiMnlXSTZndjVab3JORVNoQm9GL2kxUT09IiwibWFjIjoiZGI3ZjcwYmRjMWUxYWRiMjczYTE0MmUyZmRiNzM1OWVlZjI0NTNiOTBjZmMxYTVkODEyMmMwZjgyYjE1NzM1MyIsInRhZyI6IiJ9", "2025-06-27 11:22:24", 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.499409, "duration": 0.00864, "duration_str": "8.64ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.522, "width_percent": 9.179}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Youtube', 1, 1, 'social_media', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Youtube", 1, 1, "social_media", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.5112882, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.701, "width_percent": 0.499}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IjJCMVkzOWo1OVFxQXR4VC9MeWpaTnc9PSIsInZhbHVlIjoiZkk2TnB1aUVSb0ZRbjVhYVpkd0Z5UT09IiwibWFjIjoiZWFkMzAwNjJhZjllZTU0NmVkZjQ1MDk0MGRhOTJjNGNlOTMxMGI4MTBjZDAyNTg4NTFjOTAyYjlkNDk2YTc4OSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-06-27 11:22:24' where `id` = 3 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IjJCMVkzOWo1OVFxQXR4VC9MeWpaTnc9PSIsInZhbHVlIjoiZkk2TnB1aUVSb0ZRbjVhYVpkd0Z5UT09IiwibWFjIjoiZWFkMzAwNjJhZjllZTU0NmVkZjQ1MDk0MGRhOTJjNGNlOTMxMGI4MTBjZDAyNTg4NTFjOTAyYjlkNDk2YTc4OSIsInRhZyI6IiJ9", "2025-06-27 11:22:24", 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.514448, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.2, "width_percent": 0.584}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Facebook', 1, 1, 'social_media', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Facebook", 1, 1, "social_media", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.518738, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.784, "width_percent": 0.51}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6ImhaVVNGTnlyT1ZkNGU5YjNPTldLUnc9PSIsInZhbHVlIjoielgrN3NFclRUMlkrNGdVbXlVK2xVUT09IiwibWFjIjoiYzBiMTIyYzY4MDg2OTc3MjFkNjZmMTRhMjRlZGUyYzQ1YjljYmI2MDk5YWNjOTQ4ZmRiZmI1NDkwMmMwYjYwOSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-06-27 11:22:24' where `id` = 4 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6ImhaVVNGTnlyT1ZkNGU5YjNPTldLUnc9PSIsInZhbHVlIjoielgrN3NFclRUMlkrNGdVbXlVK2xVUT09IiwibWFjIjoiYzBiMTIyYzY4MDg2OTc3MjFkNjZmMTRhMjRlZGUyYzQ1YjljYmI2MDk5YWNjOTQ4ZmRiZmI1NDkwMmMwYjYwOSIsInRhZyI6IiJ9", "2025-06-27 11:22:24", 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.52194, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.294, "width_percent": 0.595}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('General', 1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["General", 1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5265758, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Organization.php:371", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=371", "ajax": false, "filename": "Organization.php", "line": "371"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.889, "width_percent": 1.381}, {"sql": "insert into `settings` (`key`, `organization_id`, `value`) values ('sku', 1, '1'), ('name', 1, '1'), ('barcode', 1, '0'), ('weight', 1, '1'), ('price', 1, '1'), ('compare_at_price', 1, '0'), ('cost_price', 1, '0'), ('brand', 1, '1'), ('vendor', 1, '1'), ('category', 1, '1')", "type": "query", "params": [], "bindings": ["sku", 1, "1", "name", 1, "1", "barcode", 1, "0", "weight", 1, "1", "price", 1, "1", "compare_at_price", 1, "0", "cost_price", 1, "0", "brand", 1, "1", "vendor", 1, "1", "category", 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, {"index": 11, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5309439, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Organization.php:373", "source": {"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=373", "ajax": false, "filename": "Organization.php", "line": "373"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.27, "width_percent": 1.562}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Product Name', 'product_name', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Product Name", "product_name", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.538465, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "Organization.php:434", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=434", "ajax": false, "filename": "Organization.php", "line": "434"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.832, "width_percent": 4.026}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.545614, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Organization.php:439", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=439", "ajax": false, "filename": "Organization.php", "line": "439"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.858, "width_percent": 2.114}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Title', 'title', 13, 1, null, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Title", "title", 13, 1, null, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5507162, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Organization.php:449", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=449", "ajax": false, "filename": "Organization.php", "line": "449"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.972, "width_percent": 0.691}, {"sql": "insert into `attribute_options` (`name`, `attribute_id`, `updated_at`, `created_at`) values ('Default Title', 2, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Default Title", 2, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.555479, "duration": 0.00863, "duration_str": "8.63ms", "memory": 0, "memory_str": null, "filename": "Organization.php:454", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=454", "ajax": false, "filename": "Organization.php", "line": "454"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.663, "width_percent": 9.168}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Description', 'description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":63000}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Description", "description", 3, 1, "{\"required\":1,\"max\":63000}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5671768, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Organization.php:463", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=463", "ajax": false, "filename": "Organization.php", "line": "463"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.831, "width_percent": 0.616}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 3, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, 3, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.570959, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Organization.php:468", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=468", "ajax": false, "filename": "Organization.php", "line": "468"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.447, "width_percent": 0.542}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('SEO', 1, 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["SEO", 1, 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.575796, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "Organization.php:476", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=476", "ajax": false, "filename": "Organization.php", "line": "476"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.989, "width_percent": 5.492}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('URL Slug', 'seo_url', 11, 1, '{\\\"required\\\":1,\\\"0\\\":\\\"slug\\\",\\\"1\\\":\\\"url\\\",\\\"max\\\":255}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["URL Slug", "seo_url", 11, 1, "{\"required\":1,\"0\":\"slug\",\"1\":\"url\",\"max\":255}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5901308, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Organization.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=486", "ajax": false, "filename": "Organization.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 59.482, "width_percent": 1.179}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 4, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [2, 4, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.595433, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Organization.php:491", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=491", "ajax": false, "filename": "Organization.php", "line": "491"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.661, "width_percent": 0.489}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Title', 'seo_title', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["SEO Title", "seo_title", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.599696, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Organization.php:501", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=501", "ajax": false, "filename": "Organization.php", "line": "501"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.149, "width_percent": 0.436}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 5, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [2, 5, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6040351, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "Organization.php:506", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=506", "ajax": false, "filename": "Organization.php", "line": "506"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.585, "width_percent": 4.759}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Description', 'seo_description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":160}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["SEO Description", "seo_description", 3, 1, "{\"required\":1,\"max\":160}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6112878, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Organization.php:516", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=516", "ajax": false, "filename": "Organization.php", "line": "516"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.344, "width_percent": 0.616}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 6, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [2, 6, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.61452, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Organization.php:521", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=521", "ajax": false, "filename": "Organization.php", "line": "521"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.961, "width_percent": 0.521}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Tags', 'seo_keyword', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": ["Tags", "seo_keyword", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.618637, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Organization.php:531", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=531", "ajax": false, "filename": "Organization.php", "line": "531"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 67.481, "width_percent": 0.584}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 7, '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [2, 7, "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.622372, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Organization.php:536", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=536", "ajax": false, "filename": "Organization.php", "line": "536"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.065, "width_percent": 0.552}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.625763, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Organization.php:554", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=554", "ajax": false, "filename": "Organization.php", "line": "554"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.618, "width_percent": 0.637}, {"sql": "select * from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.6290338, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.255, "width_percent": 0.648}, {"sql": "select * from `attributes` where `attributes`.`id` in (1, 3, 4, 5, 6, 7) and `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 29, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.720371, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.903, "width_percent": 0.606}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (1, 1, 'title', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [1, 1, "title", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.7246351, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Organization.php:563", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=563", "ajax": false, "filename": "Organization.php", "line": "563"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 70.509, "width_percent": 1.647}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (2, 1, 'body_html', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [2, 1, "body_html", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.72927, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:566", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=566", "ajax": false, "filename": "Organization.php", "line": "566"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.156, "width_percent": 0.595}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (4, 1, 'metafields_global_title_tag', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [4, 1, "metafields_global_title_tag", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.732734, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:581", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=581", "ajax": false, "filename": "Organization.php", "line": "581"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.75, "width_percent": 0.606}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (5, 1, 'metafields_global_description_tag', '2025-06-27 11:22:24', '2025-06-27 11:22:24')", "type": "query", "params": [], "bindings": [5, 1, "metafields_global_description_tag", "2025-06-27 11:22:24", "2025-06-27 11:22:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.737, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:584", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=584", "ajax": false, "filename": "Organization.php", "line": "584"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.356, "width_percent": 0.595}, {"sql": "select count(*) as aggregate from `invites` where `email` = '<EMAIL>' and `is_accepted` = '0' and `is_declined` = '0'", "type": "query", "params": [], "bindings": ["<EMAIL>", "0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 544}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.744428, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Invite.php:124", "source": {"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=124", "ajax": false, "filename": "Invite.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.951, "width_percent": 1.381}, {"sql": "select * from `invites` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 250}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.748361, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Invite.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=486", "ajax": false, "filename": "Invite.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.332, "width_percent": 0.659}, {"sql": "update `organizations` set `stripe_id` = 'cus_SZjqUJkPpJXBmR', `organizations`.`updated_at` = '2025-06-27 11:22:32' where `id` = 1", "type": "query", "params": [], "bindings": ["cus_SZjqUJkPpJXBmR", "2025-06-27 11:22:32", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.600879, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ManagesCustomer.php:97", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fcashier%2Fsrc%2FConcerns%2FManagesCustomer.php&line=97", "ajax": false, "filename": "ManagesCustomer.php", "line": "97"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.991, "width_percent": 0.776}, {"sql": "update `organizations` set `trial_ends_at` = '2025-07-11 11:22:32', `organizations`.`updated_at` = '2025-06-27 11:22:32' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-11 11:22:32", "2025-06-27 11:22:32", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6054409, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Organization.php:254", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=254", "ajax": false, "filename": "Organization.php", "line": "254"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.766, "width_percent": 0.584}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.609221, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Organization.php:263", "source": {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=263", "ajax": false, "filename": "Organization.php", "line": "263"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.35, "width_percent": 0.967}, {"sql": "select * from `organization_user` where `organization_user`.`organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.612787, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.317, "width_percent": 0.521}, {"sql": "insert into `organization_user` (`organization_id`, `user_id`) values (1, 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.616999, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.838, "width_percent": 1.381}, {"sql": "select `id` from `organization_user` where `user_id` = 1 and `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.622151, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Organization.php:280", "source": {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=280", "ajax": false, "filename": "Organization.php", "line": "280"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.219, "width_percent": 0.574}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.625966, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Organization.php:283", "source": {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=283", "ajax": false, "filename": "Organization.php", "line": "283"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.793, "width_percent": 1.073}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 1, '2025-06-27 11:22:32', '2025-06-27 11:22:32')", "type": "query", "params": [], "bindings": [1, 1, "2025-06-27 11:22:32", "2025-06-27 11:22:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6308658, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.866, "width_percent": 2.295}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 2, '2025-06-27 11:22:32', '2025-06-27 11:22:32')", "type": "query", "params": [], "bindings": [1, 2, "2025-06-27 11:22:32", "2025-06-27 11:22:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.636848, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.16, "width_percent": 0.616}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 3, '2025-06-27 11:22:32', '2025-06-27 11:22:32')", "type": "query", "params": [], "bindings": [1, 3, "2025-06-27 11:22:32", "2025-06-27 11:22:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.64083, "duration": 0.01326, "duration_str": "13.26ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.776, "width_percent": 14.087}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 4, '2025-06-27 11:22:32', '2025-06-27 11:22:32')", "type": "query", "params": [], "bindings": [1, 4, "2025-06-27 11:22:32", "2025-06-27 11:22:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.657212, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.863, "width_percent": 0.606}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 5, '2025-06-27 11:22:32', '2025-06-27 11:22:32')", "type": "query", "params": [], "bindings": [1, 5, "2025-06-27 11:22:32", "2025-06-27 11:22:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.660828, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.469, "width_percent": 0.531}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.672718, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:298", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=298", "ajax": false, "filename": "Organization.php", "line": "298"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Product\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}}, "count": 22, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/onboarding\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "organization_id": "1", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store", "uri": "POST api/2024-12/organization", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f41307e-45ac-499a-aa8d-fdde6be3b831\" target=\"_blank\">View in Telescope</a>", "duration": "9.38s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1971185344 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1971185344\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612819649 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tanzayb</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n  \"<span class=sf-dump-key>weightUnit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">oz</span>\"\n  \"<span class=sf-dump-key>trial_ends_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-11</span>\"\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612819649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1050285672 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVEVVFQT2hma1U2bUhhMEdkekRmSUE9PSIsInZhbHVlIjoiVGVjZVZNOTlyMDd6R2FwRDlOeWJ0T2JmUlVrYXk1ZU5mQ2RwUTBEZ2NyUDlKKzhyM0ZyQWZqSWlBdzRYcElOOVNtNGluVko3OWdhWWp4Sld5bUhDemVZT3ltNXFRa1djTjhlK3ZzQjhURWJSM2tCakxsYkdpc1ZrYTN5TGlHdGEiLCJtYWMiOiJjNDQ1YjBmNjA5OWE3NjQ4ZWQ1N2Q0MGE0ZDc4NzE5MDVlNTcwNWQxMjkzYWI3MTEyYjA0MDNhZWYzMjc4M2NlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkdqNHRtUkFQM3NtYldYMnJ5MTN0MVE9PSIsInZhbHVlIjoiaEFucnExODNnTTlPcHhTbEljRTBEVTg0cDNCVjJ5SmlvYU9OTW8wV1dOeUxNUlVGdHpVVlNaZUN2UVA3bGpySmxMOTk0VGZkdDBDWjRjbEdQRTRYRUQ5UkhJMnlHYWt4RTdTYnlaZnhwb3J4YlZ4QjFsRUtlRzdlUjY1Q1lDT0UzcVJKWG52Y1dCbHJ1RFg4ZHRoa2lnPT0iLCJtYWMiOiIyN2FkMzgwNzMxNjBkOGQyMmU1MThjNTRhMzRlMmMxNzFiNmFjMTkzMmNjYjc5YjUxMjM3Yjc0NzMxOWRmNGEwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVEVVFQT2hma1U2bUhhMEdkekRmSUE9PSIsInZhbHVlIjoiVGVjZVZNOTlyMDd6R2FwRDlOeWJ0T2JmUlVrYXk1ZU5mQ2RwUTBEZ2NyUDlKKzhyM0ZyQWZqSWlBdzRYcElOOVNtNGluVko3OWdhWWp4Sld5bUhDemVZT3ltNXFRa1djTjhlK3ZzQjhURWJSM2tCakxsYkdpc1ZrYTN5TGlHdGEiLCJtYWMiOiJjNDQ1YjBmNjA5OWE3NjQ4ZWQ1N2Q0MGE0ZDc4NzE5MDVlNTcwNWQxMjkzYWI3MTEyYjA0MDNhZWYzMjc4M2NlIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjRhdmhkK2FQSGt5bEk5WE1LYm1TRlE9PSIsInZhbHVlIjoiUnF1T1IxbFdvTFhSNmZOWUJtVGJnWHg1ak4weFp6b3JNanRjeG1zMFlzQ1R1ZWtxdUJuMG50MlJvd210bCs3Mnd0Qmg1amZBcEVyY3NsaFhUK0JOWDBxYzVPbGYrL1pkdnI3MmdYMWlrcnF1aW1wN2NEQ3VubysyUlVpZHZEVi8iLCJtYWMiOiIxZmYxYjYxNTUzNzY3MjY1YWYyYmQ2MmU1MDdiNjY0OTQ3MmRkMWUwZTk4NzMwYzlmM2Q2MGM1NmQ4MThmYWYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050285672\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92473786 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|HXrrYhx2s3t7WfXhSc1UK53hBR79an3GtHE40ciAjroQFhCSWxa5jO23zU6N|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7wipx8yTU6Zxt4273OHC75xL5Y173l3is2OEtUWH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92473786\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1826729589 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 11:22:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhnR3dTUk1obWVrSjJDZmNnWW80VHc9PSIsInZhbHVlIjoiaytKWVJYZWVxRnpGSlNxVUNBTUxPa0NvbG5YVGJOcnJkYjRqZlg2enhyOHpFbXA5TjdxVXErZm8yUncvSWRVWGJ6TzZUYTJwcTVBOG9IMGFETm9pR3dscjc5MkQ2UTQ2b3I2NmU4Z3BvaWd4bW8vcVNiR1RrbjlaWEh4NGU0STgiLCJtYWMiOiIwNWY1Yjg1NGRiZGJmNWUzYzE0NzU5MWFiMTU5MGNiMTM1ZTcwMGE2MjliNTk5ZjFjN2E2MTkwY2QzZDE5OWQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 13:22:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6ImpMOXZSdzZQL2x2Ynl0SExpSis3cHc9PSIsInZhbHVlIjoiQndNN2dCdEJnL1lOZnBkejBCMFJqSC9XRU5RNVVuZmVMS0JVZVdueEE2a2xFcTA4akRZRjMzNVRqOHdVZy8rTmJUWTI0SUpzNTdiRUY5eCt5a2pGck83SzZOK1Z6dEZVUVhYU2VnWDZhRUlyNTU3YWxNckppK2V4bUdkNkpiaHoiLCJtYWMiOiIxNTZiMGZmYzc4ZDNlZWUyZDY5MGQ3NjA3YWIyNmRjZGQxMzNkNTI5ZTU1NzhlZDM3MTkzNzk4OWM4MGMwYjMzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 13:22:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhnR3dTUk1obWVrSjJDZmNnWW80VHc9PSIsInZhbHVlIjoiaytKWVJYZWVxRnpGSlNxVUNBTUxPa0NvbG5YVGJOcnJkYjRqZlg2enhyOHpFbXA5TjdxVXErZm8yUncvSWRVWGJ6TzZUYTJwcTVBOG9IMGFETm9pR3dscjc5MkQ2UTQ2b3I2NmU4Z3BvaWd4bW8vcVNiR1RrbjlaWEh4NGU0STgiLCJtYWMiOiIwNWY1Yjg1NGRiZGJmNWUzYzE0NzU5MWFiMTU5MGNiMTM1ZTcwMGE2MjliNTk5ZjFjN2E2MTkwY2QzZDE5OWQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 13:22:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6ImpMOXZSdzZQL2x2Ynl0SExpSis3cHc9PSIsInZhbHVlIjoiQndNN2dCdEJnL1lOZnBkejBCMFJqSC9XRU5RNVVuZmVMS0JVZVdueEE2a2xFcTA4akRZRjMzNVRqOHdVZy8rTmJUWTI0SUpzNTdiRUY5eCt5a2pGck83SzZOK1Z6dEZVUVhYU2VnWDZhRUlyNTU3YWxNckppK2V4bUdkNkpiaHoiLCJtYWMiOiIxNTZiMGZmYzc4ZDNlZWUyZDY5MGQ3NjA3YWIyNmRjZGQxMzNkNTI5ZTU1NzhlZDM3MTkzNzk4OWM4MGMwYjMzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 13:22:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826729589\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1665575058 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3aRqe20JfvFXeuz6wYcGYJGkqwzQhGTt2dLQHMbb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665575058\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store"}, "badge": null}}